'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import Tokenomics from '@/components/sections/layouts/tokenomics/Tokenomics'
import { PageNav } from '@/components/common/PageNav'
import { getFunAndTrendyTokenomicsStyle } from '@/components/sections/styles/tokenomics/funandtrendy'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'

function FunAndTrendyTokenomics() {
  const searchParams = useSearchParams()
  const theme = searchParams.get('theme')
  const style = getFunAndTrendyTokenomicsStyle(theme === '2' ? 2 : 1)

  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <Tokenomics style={style} />
    </ReactLenis>
  )
}

export default function FunAndTrendyTokenomicsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <FunAndTrendyTokenomics />
    </Suspense>
  )
}
