'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react'
import PlayfulHero from '@/components/sections/layouts/hero/PlayfulHero'
import PlayfulAbout from '@/components/sections/layouts/about/PlayfulAbout'
import HowToBuy3D from '@/components/sections/layouts/howtobuy/HowToBuy3D'
import Tokenomics from '@/components/sections/layouts/tokenomics/Tokenomics'
import { PageNav } from '@/components/common/PageNav'
import { getFuturisticHeroStyle } from '@/components/sections/styles/hero/futuristicandoutofbox'
import { getFuturisticAboutStyle } from '@/components/sections/styles/about/futuristicandoutofbox'
import { getFuturisticHowToBuyStyle } from '@/components/sections/styles/howtobuy/futuristicandoutofbox'
import { getFuturisticTokenomicsStyle } from '@/components/sections/styles/tokenomics/futuristicandoutofbox'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'

function FuturisticContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const colorTemplate = Number(theme) as ColorTemplate
  
  const heroStyle = getFuturisticHeroStyle(colorTemplate)
  const aboutStyle = getFuturisticAboutStyle(colorTemplate)
  const howToBuyStyle = getFuturisticHowToBuyStyle(colorTemplate)
  const tokenomicsStyle = getFuturisticTokenomicsStyle(colorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <PlayfulHero style={heroStyle} />
      <PlayfulAbout style={aboutStyle} />
      <HowToBuy3D style={howToBuyStyle} />
      <Tokenomics style={tokenomicsStyle} />
    </ReactLenis>
  )
}

export default function FuturisticAndOutOfBoxPage() {
  return (
    <Suspense>
      <FuturisticContent />
    </Suspense>
  )
}