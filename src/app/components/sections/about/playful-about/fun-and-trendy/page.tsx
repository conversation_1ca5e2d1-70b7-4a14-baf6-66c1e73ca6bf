'use client'

import React from 'react'
import { ReactLenis } from 'lenis/react'
import PlayfulAbout from '@/components/sections/layouts/about/PlayfulAbout'
import { PageNav } from '@/components/common/PageNav'
import { funandtrendyStyle } from '@/components/sections/styles/about/funandtrendy'

export default function FunAndTrendyAboutPage() {
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <PlayfulAbout style={funandtrendyStyle} />
    </ReactLenis>
  )
}