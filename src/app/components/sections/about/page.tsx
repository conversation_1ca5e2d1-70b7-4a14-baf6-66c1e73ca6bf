"use client";

import React from "react";
import Link from "next/link";
import { PageNav } from "@/components/common/PageNav";

const aboutItems: Array<{ name: string; href: string }> = [
  {
    name: "Pudgy About",
    href: "/components/sections/about/pudgy-about",
  },
  {
    name: "MomoCoin About",
    href: "/components/sections/about/momocoin-about",
  },
];

export default function AboutPage() {
  return (
    <section className="min-h-screen py-[var(--width-10)]">
      <PageNav />
      <div className="w-full px-[var(--width-10)]">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          {aboutItems.map((item) => (
            <Link key={item.name} href={item.href}>
              <div className="aspect-square card relative rounded p-6 flex justify-center items-center text-center cursor-pointer">
                <h3 className="text-xl">{item.name}</h3>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
}
