'use client'

import React, { Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { ReactLenis } from 'lenis/react'
import PlayfulHero from '@/components/sections/layouts/hero/PlayfulHero'
import { PageNav } from '@/components/common/PageNav'
import { getFunAndTrendyHeroStyle } from '@/components/sections/styles/hero/funandtrendy'
import { ColorTemplate } from '@/components/sections/styles/shared/themeConfig'

function FunAndTrendyHeroContent() {
  const searchParams = useSearchParams()
  const theme = (searchParams.get('theme') as unknown as ColorTemplate) || 1
  const heroStyle = getFunAndTrendyHeroStyle(Number(theme) as ColorTemplate)
  
  return (
    <ReactLenis root>
      <PageNav position="bottom" />
      <PlayfulHero style={heroStyle} />
    </ReactLenis>
  )
}

export default function FunAndTrendyPage() {
  return (
    <Suspense>
      <FunAndTrendyHeroContent />
    </Suspense>
  )
}