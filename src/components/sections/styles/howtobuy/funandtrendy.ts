
import { HowToBuyStyle } from './types';
import { funAndTrendyTheme as theme } from '../shared/themes';
import { ColorTemplate } from '../shared/themeConfig';
import { getFunAndTrendyColors } from '../shared/themeConfig';

export function getFunAndTrendyHowToBuyStyle(colorTemplate: ColorTemplate = 1): HowToBuyStyle {
    const colors = getFunAndTrendyColors(colorTemplate);
    
    return {
        section: {
            className: `${theme.spacing.sectionPadding} ${theme.borders.section}`,
            backgroundColor: colors.tertiary,
            backgroundPattern: theme.backgrounds.texture
        },
        title: {
            text: "HOW TO BUY",
            className: `text-8xl md:!text-[clamp(4.75rem,7.5vw,7.5rem)] text-center ${theme.text.headingClass} ${theme.text.white} ${theme.fonts.heading.className}`,
            useRetroText: true,
            animation: 'slide',
            shadowOffset: theme.shadows.retro.offset,
            shadowColor: theme.shadows.retro.color,
            animationProps: {
                duration: theme.animations.duration,
                stagger: theme.animations.stagger,
                start: 'top 80%',
                end: 'top 20%',
                variant: theme.animations.variant
            }
        },
        bento: {
            items: [
                {
                    position: 'left',
                    image: '/sections/images/character1.webp',
                    titleEN: 'Step 1: Create Wallet',
                    descriptionEN: 'Download and set up MetaMask or your preferred crypto wallet to store.'
                },
                {
                    position: 'center',
                    image: '/sections/images/character2.webp',
                    titleEN: 'Step 2: Get ETH',
                    descriptionEN: 'Purchase Ethereum from an exchange and transfer it to your wallet address.',
                    isCenter: true
                },
                {
                    position: 'right',
                    image: '/sections/images/character3.webp',
                    titleEN: 'Step 3: Swap for $PUDGY',
                    descriptionEN: 'Connect to Uniswap decentralized exchange and swap your ETH.'
                }
            ],
            className: '',
            gridClassName: '',
            itemClassName: '',
            textContainerClassName: 'gap-3',
            imageContainerClassName: `!h-70 ${colors.tertiary} flex items-end`,
            imageClassName: '!h-3/4 w-auto object-contain',
            titleClassName: `${theme.fonts.heading.className} text-2xl md:!text-4xl font-bold`,
            descriptionClassName: `${theme.fonts.body.className} text-base md:!text-xl !leading-[1.1] font-semibold`,
            enableAnimation: true
        }
    };
}

export const funandtrendyStyle = getFunAndTrendyHowToBuyStyle(1);