import { HowToBuyStyle } from './types';
import { futuristicTheme as theme } from '../shared/themes';
import { ColorTemplate } from '../shared/themeConfig';
import { getFuturisticColors } from '../shared/themeConfig';

export function getFuturisticHowToBuyStyle(colorTemplate: ColorTemplate = 1): HowToBuyStyle {
    const colors = getFuturisticColors(colorTemplate);
    
    return {
        section: {
            className: theme.spacing.sectionPadding,
            backgroundColor: colors.primary,
            spotlight: {
                width: '25%',
                height: '150%',
                left: '0%',
                top: '-30%',
                rotate: '-60deg',
                color: colors.spotlight,
                blur: '100px',
                opacity: 1,
                mobileWidth: '55%',
                mobileHeight: '70%',
                mobileLeft: '-10%',
                mobileTop: '-60%',
                mobileRotate: '-30deg'
            }
        },
        title: {
            text: "How to Buy",
            className: `text-6xl md:text-8xl text-center ${theme.text.headingClass} leading-[1.1] ${theme.fonts.heading.className}`,
            useRetroText: false,
            animationProps: {
                duration: theme.animations.duration,
                stagger: theme.animations.stagger,
                start: 'top 80%',
                end: 'top 20%',
                variant: theme.animations.variant
            },
            gradientColors: theme.gradients.text
        },
        bento: {
            items: [
                {
                    position: 'left',
                    image: '/sections/images/character1.webp',
                    titleEN: 'Step 1: Create Wallet',
                    descriptionEN: 'Download and set up MetaMask or your preferred crypto wallet to store.'
                },
                {
                    position: 'center',
                    image: '/sections/images/character2.webp',
                    titleEN: 'Step 2: Get ETH',
                    descriptionEN: 'Purchase Ethereum from an exchange and transfer it to your wallet address.',
                    isCenter: true
                },
                {
                    position: 'right',
                    image: '/sections/images/character3.webp',
                    titleEN: 'Step 3: Swap for $PUDGY',
                    descriptionEN: 'Connect to Uniswap decentralized exchange and swap your ETH.'
                }
            ],
            className: '',
            gridClassName: '',
            itemClassName: `${colors.cardBg} futuristic-card-border ${theme.text.white}`,
            textContainerClassName: 'gap-1',
            imageContainerClassName: '!h-70 bg-white/10 flex items-end',
            imageClassName: '!h-3/4 w-auto object-contain',
            titleClassName: `text-2xl md:!text-3xl font-bold`,
            descriptionClassName: `text-sm md:!text-base !leading-[1.1]`,
            enableAnimation: true
        }
    };
}

export const futuristicandoutofboxStyle = getFuturisticHowToBuyStyle(1);