import { HeroStyle } from "./types";
import { luckiestGuy, funAndTrendyTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFunAndTrendyColors } from "../shared/themeConfig";
import { Clipboard } from "lucide-react";

export function getFunAndTrendyHeroStyle(
  colorTemplate: ColorTemplate = 1
): HeroStyle {
  const colors = getFunAndTrendyColors(colorTemplate);

  return {
    navbar: {
      logoSrc: "/sections/images/funandtrendylogo.svg",
      buttonBgColor: colors.button,
      buttonHoverBgColor: colors.buttonHover,
      buttonTextColor: "text-white",
      buttonHoverTextColor: colors.buttonHoverText,
      buttonClassName: `h-13 px-8 ${theme.borders.button}`,
      buttonContentClassName: `!text-2xl ${theme.text.headingClass} ${theme.fonts.body.className}`,
      className: "top-12",
      logoClassName: "h-18",
    },
    section: {
      className: colors.secondary,
      height: "h-svh md:h-[130vh]",
      contentAlignment: "items-center",
      backgroundPattern: theme.backgrounds.texture,
      backgroundImage: "/sections/images/pepohero.jpeg",
      textContainerClassName: `${theme.text.white} ${theme.fonts.heading.className}`,
      gapClassName: theme.spacing.gap,
    },
    heading: {
      text: "Pepo's On The Blockchain",
      className: `text-6xl [text-shadow:2px_2px_0_#000] [-webkit-text-stroke:1px_#000] md:text-[clamp(5rem,7vw,8rem)] mx-auto text-center w-full pt-3 tracking-tight ${theme.text.white}`,
      animation: "slide",
      shadowColor: theme.shadows.retro.color,
      shadowOffset: theme.shadows.retro.offset,
      animationProps: {
        duration: theme.animations.duration,
        stagger: theme.animations.stagger,
        start: "top 80%",
        end: "top 20%",
        variant: theme.animations.variant,
      },
    },
    subheading: {
      text: "Pepo leaps to the moon with meme magic & Sui speed.",
      className: `w-full md:w-5/7 text-base md:text-3xl mt-4 md:mt-6 ${theme.text.bodyClass} leading-[1.1] ${theme.fonts.body.className} text-white`,
    },
    cta: (
      <div
        className={
          "w-full py-3 px-6 bg-white border-2 flex items-center justify-between sm:max-w-[430px] mx-auto " +
          luckiestGuy.className
        }
        style={{
          boxShadow: "4px 4px 0px rgba(0, 0, 0)",
        }}
      >
        <span className="text-slate-800/80 truncate text-sm md:text-lg">
          0x2aeslsdjnaisjjiasdl8jmnn
        </span>
        <span className="text-sm shrink-0">
          Copy
          <Clipboard className="inline-block ml-1 h-4" />
        </span>
      </div>
    ),
  };
}

export const funandtrendyStyle = getFunAndTrendyHeroStyle(1);
