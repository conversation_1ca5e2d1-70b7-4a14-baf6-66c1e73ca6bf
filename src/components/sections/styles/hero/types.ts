import React from "react";
import {
  BaseSection,
  BaseTextConfig,
  NavbarConfig,
  GradientConfig,
} from "../shared/types";

export interface HeroStyle {
  navbar: NavbarConfig;
  section: BaseSection & {
    height?: string;
    contentAlignment?: string;
    customGradient?: string;
    sideGlowGradient?: GradientConfig;
    textContainerClassName?: string;
    gapClassName?: string;
  };
  heading: BaseTextConfig;
  subheading: Pick<BaseTextConfig, "text" | "className">;
  cta?: React.ReactNode;
}
