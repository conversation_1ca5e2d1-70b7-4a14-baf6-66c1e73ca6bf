import { HeroStyle } from './types';
import { futuristicTheme as theme } from '../shared/themes';
import { ColorTemplate } from '../shared/themeConfig';
import { getFuturisticColors } from '../shared/themeConfig';

export function getFuturisticHeroStyle(colorTemplate: ColorTemplate = 1): HeroStyle {
    const colors = getFuturisticColors(colorTemplate);
    
    const sideGlowGradient = {
        radialColor: theme.backgrounds.gradient.radialColor,
        linearColor: colors.gradientLinear,
        radialOpacity: theme.backgrounds.gradient.radialOpacity,
        linearOpacity: theme.backgrounds.gradient.linearOpacity
    };
    
    return {
        navbar: {
            logoSrc: "/images/logowhite.svg",
            buttonBgColor: 'bg-white/10 backdrop-blur-md',
            buttonHoverBgColor: 'after:bg-white',
            buttonTextColor: theme.text.white,
            buttonHoverTextColor: 'after:text-black',
            buttonClassName: theme.borders.button,
            buttonContentClassName: `!text-base ${theme.fonts.body.className}`,
            className: "top-8",
            logoClassName: "h-8"
        },
        section: {
            className: colors.primary,
            height: "h-svh md:h-screen",
            contentAlignment: "items-center",
            sideGlowGradient: sideGlowGradient,
            textContainerClassName: `${theme.text.white} ${theme.fonts.heading.className}`,
            gapClassName: theme.spacing.gap,
            sparkles: {
                particleColor: '#ffffff',
                particleDensity: 80,
                minSize: 0.5,
                maxSize: 1.5,
                speed: 2
            }
        },
        heading: {
            text: "The Huddle",
            className: `text-9xl md:text-[clamp(3rem,12.5vw,12.5rem)] !tracking-tight uppercase font-extrabold leading-[1.1] mt-[-5%]`,
            useRetroText: false,
            animationProps: {
                duration: theme.animations.duration,
                stagger: theme.animations.stagger,
                start: 'top 80%',
                end: 'top 20%',
                variant: theme.animations.variant
            },
            gradientColors: theme.gradients.text
        },
        subheading: {
            text: "Pudgy Penguins is a global IP focused on proliferating the penguin, memetic culture, and good vibes.",
            className: `md:max-w-[55%] text-sm md:text-xl leading-[1.2] ${theme.text.muted} ${theme.fonts.body.className}`
        }
    };
}

export const futuristicandoutofboxStyle = getFuturisticHeroStyle(1);