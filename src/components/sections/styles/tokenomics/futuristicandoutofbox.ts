import { TokenomicsStyle } from "./types";
import { futuristicTheme as theme } from "../shared/themes";
import { ColorTemplate } from "../shared/themeConfig";
import { getFuturisticColors } from "../shared/themeConfig";

export function getFuturisticTokenomicsStyle(
  colorTemplate: ColorTemplate = 1
): TokenomicsStyle {
  const colors = getFuturisticColors(colorTemplate);

  return {
    section: {
      className: theme.spacing.sectionPadding,
      backgroundColor: colors.primary,
      spotlight: {
        width: "25%",
        height: "150%",
        left: "0%",
        top: "-30%",
        rotate: "-60deg",
        color: colors.spotlight,
        blur: "100px",
        opacity: 1,
        mobileWidth: "55%",
        mobileHeight: "70%",
        mobileLeft: "-10%",
        mobileTop: "-60%",
        mobileRotate: "-30deg",
      },
    },
    title: {
      text: "Tokenomics",
      className: `text-6xl md:text-8xl leading-none tracking-tight ${theme.text.headingClass} ${theme.fonts.heading.className}`,
      animation: "slide",
      useRetroText: false,
      animationProps: {
        duration: theme.animations.duration,
        stagger: theme.animations.stagger,
        start: "top 80%",
        end: "top 20%",
        variant: theme.animations.variant,
      },
      gradientColors: theme.gradients.text,
    },
    description: {
      text: "With zero taxes, locked liquidity, and renounced ownership, Polly is built for the community—fair, transparent, and unstoppable.",
      className: `mt-4 text-lg font-medium max-w-3xl ${theme.fonts.body.className} text-off-white`,
    },
    bento: {
      items: [
        { value: "Ticker", description: "$Polly" },
        { value: "Network", description: "Abstract" },
        { value: "Supply", description: "1,000,000,000" },
        { value: "Liquidity", description: "Locked" },
        { value: "None", description: "None" },
        { value: "Ownership", description: "Renounced" },
      ],
      className: "",
      gridClassName: "gap-3 md:gap-6",
      itemClassName: `${colors.cardBg} futuristic-card-border p-8 md:p-10 !gap-1 md:!gap-3 justify-center items-center`,
      valueClassName: `text-2xl sm:text-4xl font-semibold ${theme.fonts.heading.className}`,
      gradientColors: theme.gradients.text,
      descriptionClassName: `text-lg sm:text-xl font-medium ${theme.fonts.body.className} text-off-white`,
    },
  };
}

export const futuristicTokenomicsStyle = getFuturisticTokenomicsStyle(1);
