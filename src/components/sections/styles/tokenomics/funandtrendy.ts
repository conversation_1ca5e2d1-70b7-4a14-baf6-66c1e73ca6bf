import { TokenomicsStyle } from './types';
import { funAndTrendyTheme as theme } from '../shared/themes';
import { ColorTemplate } from '../shared/themeConfig';
import { getFunAndTrendyColors } from '../shared/themeConfig';

export function getFunAndTrendyTokenomicsStyle(colorTemplate: ColorTemplate = 1): TokenomicsStyle {
    const colors = getFunAndTrendyColors(colorTemplate);

    return {
        section: {
            className: `${theme.spacing.sectionPadding} ${theme.borders.section}`,
            backgroundColor: colors.secondary,
            backgroundPattern: theme.backgrounds.texture
        },
        title: {
            text: "Tokenomics",
            className: `text-8xl md:!text-[clamp(4.75rem,7.5vw,7.5rem)] leading-none uppercase tracking-tight mb-6 ${theme.text.headingClass} ${theme.text.white} ${theme.fonts.heading.className}`,
            shadowOffset: theme.shadows.retro.offset,
            useRetroText: true,
            animation: 'slide',
            shadowColor: theme.shadows.retro.color,
            animationProps: {
                duration: theme.animations.duration,
                stagger: theme.animations.stagger,
                start: 'top 80%',
                end: 'top 20%',
                variant: theme.animations.variant
            }
        },
        description: {
            text: "With zero taxes, locked liquidity, and renounced ownership, Polly is built for the community—fair, transparent, and unstoppable.",
            className: `${theme.text.white} text-base md:text-xl ${theme.text.bodyClass} ${theme.fonts.body.className}`,
        },
        bento: {
            items: [
                { value: 'Ticker', description: '$Polly' },
                { value: 'Network', description: 'Abstract' },
                { value: 'Supply', description: '1,000,000,000' },
                { value: 'Liquidity', description: 'Locked' },
                { value: 'None', description: 'None' },
                { value: 'Ownership', description: 'Renounced' }
            ],
            className: '!mt-0',
            gridClassName: 'gap-3 md:gap-6',
            itemClassName: 'bg-white border-4 rounded-2xl p-5 md:p-6 !gap-4 justify-center items-center',
            valueClassName: `text-2xl sm:text-4xl font-bold ${theme.fonts.heading.className}`,
            descriptionClassName: `text-lg sm:text-2xl font-medium ${theme.fonts.body.className}`
        },
    };
}

export const funAndTrendyTokenomicsStyle = getFunAndTrendyTokenomicsStyle(1);
