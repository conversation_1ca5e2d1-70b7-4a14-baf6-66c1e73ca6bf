import { SpotlightProps } from '@/components/background/Spotlight';
import { AnimationProps, BaseTextConfig } from '../shared/types';

export interface KPIItem {
    value: string;
    description: string;
}

export interface TokenomicsStyle {
    section: {
        className?: string;
        backgroundColor?: string;
        backgroundPattern?: string;
        spotlight?: SpotlightProps;
    };
    title: BaseTextConfig
    description: {
        text: string;
        className?: string;
    };
    bento: {
        items: KPIItem[];
        className?: string;
        gridClassName?: string;
        itemClassName?: string;
        valueClassName?: string;
        descriptionClassName?: string;
        gradientColors?: {
            from: string;
            to: string;
        };
    };
}
