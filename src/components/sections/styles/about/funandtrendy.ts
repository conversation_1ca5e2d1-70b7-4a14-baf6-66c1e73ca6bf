import { AboutStyle } from './types';
import { funAndTrendyTheme as theme } from '../shared/themes';
import { ColorTemplate } from '../shared/themeConfig';
import { getFunAndTrendyColors } from '../shared/themeConfig';

export function getFunAndTrendyAboutStyle(colorTemplate: ColorTemplate = 1): AboutStyle {
    const colors = getFunAndTrendyColors(colorTemplate);
    
    return {
        section: {
            className: `${theme.spacing.sectionPadding} ${theme.borders.section}`,
            backgroundColor: colors.primary,
            backgroundPattern: theme.backgrounds.texture
        },
        title: {
            text: "PUDGY PENGUINS",
            className: `text-6xl md:!text-[clamp(4.75rem,7.5vw,7.5rem)] !tracking-normal ${theme.text.headingClass} ${theme.text.white} ${theme.fonts.heading.className}`,
            useRetroText: true,
            animation: 'slide',
            shadowOffset: theme.shadows.retro.offset,
            shadowColor: theme.shadows.retro.color,
            animationProps: {
                duration: theme.animations.duration,
                stagger: theme.animations.stagger,
                start: 'top 80%',
                end: 'top 20%',
                variant: theme.animations.variant
            }
        },
        descriptions: {
            texts: [
                "Welcome to the world of Pudgy Penguins, a web3-born brand that fosters creativity, freedom, and community.",
                "The Pudgy Penguins brand produces content, merchandise, toys, and digital collectables. We believe in the power of play and imagination, and we're committed to helping you unlock your inner child.",
                "It's a very cold place but you'll be warm with your new favorite penguin family!"
            ],
            className: `${theme.text.white} text-base md:text-2xl ${theme.text.bodyClass} ${theme.fonts.body.className}`,
            containerClassName: "flex flex-col gap-4 md:gap-6"
        },
        layout: {
            alignStart: true,
            descriptionClassName: "md:!w-[50%]"
        }
    };
}

export const funandtrendyStyle = getFunAndTrendyAboutStyle(1);