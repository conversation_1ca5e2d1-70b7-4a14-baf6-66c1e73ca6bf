import { AboutStyle } from './types';
import { futuristicTheme as theme } from '../shared/themes';
import { ColorTemplate } from '../shared/themeConfig';
import { getFuturisticColors } from '../shared/themeConfig';

export function getFuturisticAboutStyle(colorTemplate: ColorTemplate = 1): AboutStyle {
    const colors = getFuturisticColors(colorTemplate);
    return {
        section: {
            className: `${theme.spacing.sectionPadding} !pb-0`,
            backgroundColor: colors.primary,
            showBorder: true
        },
        title: {
            text: "About Us",
            className: `text-6xl md:text-8xl ${theme.text.headingClass} leading-[1.1] ${theme.fonts.heading.className}`,
            useRetroText: false,
            animationProps: {
                duration: theme.animations.duration,
                stagger: 0.1,
                start: 'top 80%',
                end: 'top 20%',
                variant: 'words-trigger' as const
            },
            gradientColors: theme.gradients.text
        },
        descriptions: {
            texts: [
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec sit amet libero et lectus commodo bibendum. Sed semper orci leo, vel auctor massa vestibulum et. Integer cursus in dui id mattis.",
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec sit amet libero et lectus commodo bibendum. Sed semper orci leo, vel auctor massa vestibulum et. Integer cursus in dui id mattis.",
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec sit amet libero et lectus commodo bibendum. Sed semper orci leo, vel auctor massa vestibulum et. Integer cursus in dui id mattis."
            ],
            className: `${theme.text.muted} text-base md:text-xl ${theme.fonts.body.className}`,
            containerClassName: "flex flex-col gap-4 md:gap-6"
        },
        layout: {
            alignStart: true,
            descriptionClassName: "md:!w-[50%]"
        }
    };
}

export const futuristicandoutofboxStyle = getFuturisticAboutStyle(1);