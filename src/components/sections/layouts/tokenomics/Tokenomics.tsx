'use client';

import React, { memo } from 'react';
import SimpleKPIBento from '@/components/bento/SimpleKPIBento';
import TextRenderer from '@/components/sections/layouts/TextRenderer';
import dynamic from 'next/dynamic';
import { TokenomicsStyle } from '@/components/sections/styles/tokenomics/types';
import HorizontalTextbox from '@/components/textbox/HorizontalTextbox';

const Spotlight = dynamic(() => import('@/components/background/Spotlight'), {
    ssr: false
});

interface TokenomicsProps {
    style: TokenomicsStyle;
}

const Tokenomics = ({ style }: TokenomicsProps) => {
    return (
        <section className={`w-full relative overflow-hidden ${style.section.backgroundColor} ${style.section.className}`}>
            {style.section.backgroundPattern && (
                <div className={`absolute inset-0 opacity-20 ${style.section.backgroundPattern} bg-repeat`} />
            )}
            {style.section.spotlight && (
                <Spotlight {...style.section.spotlight} />
            )}
            <div className="max-w-[var(--width-100)] px-[var(--width-10)] mx-auto relative z-10 flex flex-col gap-6">
                <HorizontalTextbox
                    title={<TextRenderer config={style.title} as="h1" />}
                    description={
                        <p className={style.description.className}>
                            {style.description.text}
                        </p>
                    }
                    className="!gap-0 md:!gap-6"
                />
                <SimpleKPIBento
                    items={style.bento.items}
                    className={style.bento.className}
                    gridClassName={style.bento.gridClassName}
                    itemClassName={style.bento.itemClassName}
                    valueClassName={style.bento.valueClassName}
                    descriptionClassName={style.bento.descriptionClassName}
                    gradientColors={style.bento.gradientColors}
                />
            </div>
        </section>
    );
};

Tokenomics.displayName = 'Tokenomics';

export default memo(Tokenomics);
