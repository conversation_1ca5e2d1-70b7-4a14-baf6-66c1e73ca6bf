"use client";

import { memo } from "react";
import TextRenderer from "@/components/sections/layouts/TextRenderer";
import { AboutStyle } from "@/components/sections/styles/about/types";
import Image from "next/image";
import VerticalTextbox from "@/components/textbox/Verticaltextbox";
import {
  Framer,
  Github,
  Instagram,
  Twitter,
  type LucideIcon,
} from "lucide-react";
import SideGlowGradientBackground from "@/components/background/SideGlowGradientBackground";
import PushableButton from "@/components/buttons/PushableButton";

interface MomoCoinAboutProps {
  style: AboutStyle;
}

const MomoCoinAbout = ({ style }: MomoCoinAboutProps) => {
  const socialIcons: Array<LucideIcon> = [Github, Instagram, Framer, Twitter];

  return (
    <section
      className={`w-full relative ${style.section.backgroundColor} ${style.section.className} ${
        style.section.fadeBottom ? '[mask-image:linear-gradient(to_bottom,black_0%,black_90%,transparent_100%)]' : ''
      }`}
    >
      {style.gradient?.show && (
        <SideGlowGradientBackground
          inset={style.gradient.inset}
          rounded={style.gradient.rounded}
          radialColor={style.gradient.radialColor}
          linearColor={style.gradient.linearColor}
          radialOpacity={style.gradient.radialOpacity}
          linearOpacity={style.gradient.linearOpacity}
          linearOpacityMobile={style.gradient.linearOpacityMobile}
        />
      )}
      {style.section.backgroundPattern && (
        <div
          className={`absolute inset-0 opacity-20 ${style.section.backgroundPattern} bg-repeat`}
        />
      )}
      <div className="max-w-[var(--width-90)] px-[var(--vw-2)] md:px-12 mx-auto relative z-10 grid md:grid-cols-2 gap-8 md:gap-10">
        <div className="flex flex-col justify-between items-start gap-4">
          <VerticalTextbox
            title={<TextRenderer config={style.title} as="h2" />}
            description={
              <div className={style.descriptions.containerClassName}>
                {style.descriptions.texts.map((description, idx) => (
                  <p key={idx} className={style.descriptions.className}>
                    {description}
                  </p>
                ))}
              </div>
            }
            className={style.layout.textboxClassName}
            alignStart={true}
            descriptionClassName={style.layout.descriptionClassName}
          />
          <div className="flex gap-6 mt-2 md:mt-0">
            {socialIcons.map((Icon, idx) => (
              <PushableButton
                key={idx}
                type="button"
                variant={style.button?.variant || "side"}
                className={style.button?.className}
                frontClassName={style.button?.childClassName}
                ariaLabel={`Social icon ${idx + 1}`}
              >
                <Icon
                  strokeWidth={1.25}
                  className={style.button?.iconClassName}
                />
              </PushableButton>
            ))}
          </div>
        </div>
        <div className={style.image?.parentClassName}>
          <Image
            src={"/sections/images/momo-about.png"}
            alt="momo about"
            width={600}
            height={500}
            className={style.image?.className}
          />
        </div>
        {style.section.showBorder && (
          <div className="w-full h-px bg-white/10 mt-20 md:mt-30" />
        )}
      </div>
    </section>
  );
};

MomoCoinAbout.displayName = "MomoCoinAbout";

export default memo(MomoCoinAbout);
