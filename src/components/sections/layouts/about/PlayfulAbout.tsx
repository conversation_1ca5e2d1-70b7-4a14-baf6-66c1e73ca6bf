'use client';

import React, { memo } from 'react';
import HorizontalTextbox from '@/components/textbox/HorizontalTextbox';
import TextRenderer from '@/components/sections/layouts/TextRenderer';
import { AboutStyle } from '@/components/sections/styles/about/types';

interface PlayfulAboutProps {
    style: AboutStyle;
}

const PlayfulAbout = ({ 
    style
}: PlayfulAboutProps) => {
    return (
        <section className={`w-full relative ${style.section.backgroundColor} ${style.section.className}`}>
            {style.section.backgroundPattern && (
                <div className={`absolute inset-0 opacity-20 ${style.section.backgroundPattern} bg-repeat`} />
            )}
            <div className="max-w-[var(--width-100)] px-[var(--width-10)] mx-auto relative z-10">
                <HorizontalTextbox
                    title={<TextRenderer config={style.title} as="h2" />}
                    description={
                        <div className={style.descriptions.containerClassName}>
                            {style.descriptions.texts.map((text, index) => (
                                <p key={index} className={style.descriptions.className}>
                                    {text}
                                </p>
                            ))}
                        </div>
                    }
                    alignStart={style.layout.alignStart}
                    titleClassName="mb-3 md:mt-0 md:!w-30"
                    descriptionClassName={style.layout.descriptionClassName}
                />
                {style.section.showBorder && (
                    <div className="w-full h-px bg-white/10 mt-20 md:mt-30" />
                )}
            </div>
        </section>
    );
};

PlayfulAbout.displayName = 'PlayfulAbout';

export default memo(PlayfulAbout);