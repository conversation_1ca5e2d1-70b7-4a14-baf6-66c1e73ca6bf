"use client";

import { memo } from "react";
import SimpleNavbar from "@/components/navigation/SimpleNavbar";
import SideGlowGradientBackground from "@/components/background/SideGlowGradientBackground";
import dynamic from "next/dynamic";
import { HeroStyle } from "@/components/sections/styles/hero/types";
import { luckiestGuy } from "../../styles/shared/themes";
import { cls } from "@/lib/utils";
import Image from "next/image";
import TextRenderer from "../TextRenderer";

const SparklesCore = dynamic(
  () =>
    import("@/components/sparkles/Sparkles").then((mod) => ({
      default: mod.SparklesCore,
    })),
  {
    ssr: false,
  }
);

interface PepeHeroProps {
  style: HeroStyle;
  onMenuClick?: () => void;
  onContactClick?: () => void;
}

const PepeHero = ({ style, onMenuClick, onContactClick }: PepeHeroProps) => {
  return (
    <>
      <SimpleNavbar
        logoSrc={style.navbar.logoSrc}
        leftButtonText="Menu"
        rightButtonText="Contact"
        buttonBgColor={style.navbar.buttonBgColor}
        buttonHoverBgColor={style.navbar.buttonHoverBgColor}
        buttonTextColor={style.navbar.buttonTextColor}
        buttonHoverTextColor={style.navbar.buttonHoverTextColor}
        buttonClassName={style.navbar.buttonClassName}
        buttonContentClassName={style.navbar.buttonContentClassName}
        className={style.navbar.className}
        logoClassName={style.navbar.logoClassName}
        onLeftButtonClick={onMenuClick}
        onRightButtonClick={onContactClick}
      />
      <section
        className={cls(
          "w-full relative overflow-hidden",
          style.section.height,
          style.section.className
        )}
      >
        {style.section.sideGlowGradient ? (
          <>
            <SideGlowGradientBackground
              radialColor={style.section.sideGlowGradient.radialColor}
              linearColor={style.section.sideGlowGradient.linearColor}
              radialOpacity={style.section.sideGlowGradient.radialOpacity}
              linearOpacity={style.section.sideGlowGradient.linearOpacity}
            />
            {style.section.sparkles && (
              <div className="absolute inset-3 md:inset-8 rounded-xl opacity-40">
                <SparklesCore
                  {...style.section.sparkles}
                  className="absolute inset-0"
                  background="transparent"
                />
              </div>
            )}
          </>
        ) : style.section.customGradient ? (
          <div
            className="absolute inset-3 md:inset-8 rounded-xl overflow-hidden"
            style={{ background: style.section.customGradient }}
          >
            {style.section.sparkles && (
              <div className="absolute inset-0 opacity-40">
                <SparklesCore
                  {...style.section.sparkles}
                  className="absolute inset-0"
                  background="transparent"
                />
              </div>
            )}
          </div>
        ) : (
          <div
            className={`absolute inset-0 opacity-30 ${style.section.backgroundPattern} bg-repeat`}
          />
        )}
        <div
          className={cls(
            "max-w-[var(--width-100)] px-[var(--width-10)] w-full h-svh flex justify-center relative mx-auto z-20",
            style.section.contentAlignment
          )}
        >
          <div className="w-full flex flex-col gap-2 md:gap-3 text-center items-center">
            <div className={luckiestGuy.className}>
              <TextRenderer
                config={{
                  text: style.heading.text,
                  className: style.heading.className,
                  animation: style.heading.animation,
                  shadowColor: style.heading.shadowColor,
                  shadowOffset: style.heading.shadowOffset,
                  animationProps: style.heading.animationProps,
                }}
              />
            </div>
            <div className={style.subheading.className}>
              {style.subheading.text}
            </div>
            <div className="w-full mt-8 md:mt-12">{style.cta}</div>
          </div>
        </div>
        {style.section.backgroundImage && (
          <div className="md:w-screen w-[155vw] absolute z-0 bottom-0 left-1/2 -translate-x-1/2">
            <Image
              src={style.section.backgroundImage}
              width={1400}
              height={1000}
              alt="Background decoration"
              className="w-full h-auto [mask-image:linear-gradient(to_bottom,transparent_0%,black_30%,black_80%,transparent_100%,)] [-webkit-mask-image:linear-gradient(to_bottom,transparent_0%,black_40%,black_80%,transparent_100%)]"
            />
          </div>
        )}
      </section>
    </>
  );
};

PepeHero.displayName = "PepeHero";

export default memo(PepeHero);
