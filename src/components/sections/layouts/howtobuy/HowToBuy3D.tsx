'use client';

import React, { memo } from 'react';
import ThreeDBento from '@/components/bento/threeDBento/ThreeDBento';
import TextRenderer from '@/components/sections/layouts/TextRenderer';
import dynamic from 'next/dynamic';

const Spotlight = dynamic(() => import('@/components/background/Spotlight'), {
  ssr: false
});
import { HowToBuyStyle } from '@/components/sections/styles/howtobuy/types';

interface HowToBuy3DProps {
    style: HowToBuyStyle;
}

const HowToBuy3D = ({
    style
}: HowToBuy3DProps) => {
    return (
        <section className={`w-full relative overflow-hidden ${style.section.backgroundColor} ${style.section.className}`}>
            {style.section.backgroundPattern && (
                <div className={`absolute inset-0 opacity-20 ${style.section.backgroundPattern} bg-repeat`} />
            )}
            {style.section.spotlight && (
                <Spotlight {...style.section.spotlight} />
            )}
            <div className=" max-w-[var(--width-100)] px-[var(--width-10)] mx-auto relative z-10 flex flex-col gap-6 md:gap-12">
                <div className="flex items-center justify-center overflow-hidden" >
                    <TextRenderer config={style.title} as="h2" />
                </div>
                <ThreeDBento
                    items={style.bento.items}
                    enableAnimation={style.bento.enableAnimation}
                    className={style.bento.className}
                    gridClassName={style.bento.gridClassName}
                    itemClassName={style.bento.itemClassName}
                    imageContainerClassName={style.bento.imageContainerClassName}
                    imageClassName={style.bento.imageClassName}
                    textContainerClassName={style.bento.textContainerClassName}
                    titleClassName={style.bento.titleClassName}
                    descriptionClassName={style.bento.descriptionClassName}
                />
            </div>
        </section>
    );
};

HowToBuy3D.displayName = 'HowToBuy3D';

export default memo(HowToBuy3D);