"use client";
import React, { memo } from 'react';
import Image from 'next/image';
import StaggerButton from '../buttons/staggerButton/StaggerButton';

interface MinimalNavbarProps {
    logoSrc?: string;
    logoWidth?: number;
    logoHeight?: number;
    buttonText?: string;
    onButtonClick?: () => void;
    className?: string;
}

const MinimalNavbar = memo<MinimalNavbarProps>(function MinimalNavbar({
    logoSrc = "/images/logo.svg",
    logoWidth = 120,
    logoHeight = 40,
    buttonText = "Join Now",
    onButtonClick = () => {},
    className = ""
}) {
    return (
        <nav
            role="navigation"
            aria-label="Main navigation"
            className={`
                fixed z-[100] flex items-center justify-between
                top-6 left-[var(--width-10)] right-[var(--width-10)]
                transition-all duration-500 ease-in-out
                ${className}
            `}
        >
            <Image
                src={logoSrc}
                width={logoWidth}
                height={logoHeight}
                className="h-[var(--text-xl)] w-auto"
                alt="Company Logo"
                priority
            />
            
            <StaggerButton
                text={buttonText}
                onClick={onButtonClick}
                className="relative px-6 h-10 z-100"
                bgClassName="rounded-full"
                aria-label={buttonText}
            />
        </nav>
    );
});

export default MinimalNavbar;